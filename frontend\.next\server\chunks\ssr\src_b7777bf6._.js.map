{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/utils/getNextStatus.js"], "sourcesContent": ["export default function getNextStatus(status) {\r\n    const statusOrder = [\"new\", \"confirmed\", \"completed\", \"delivery\", \"cancelled\"];\r\n    const currentIndex = statusOrder.indexOf(status);\r\n    return currentIndex < statusOrder.length - 1 ? statusOrder[currentIndex + 1] : status;\r\n}"], "names": [], "mappings": ";;;AAAe,SAAS,cAAc,MAAM;IACxC,MAAM,cAAc;QAAC;QAAO;QAAa;QAAa;QAAY;KAAY;IAC9E,MAAM,eAAe,YAAY,OAAO,CAAC;IACzC,OAAO,eAAe,YAAY,MAAM,GAAG,IAAI,WAAW,CAAC,eAAe,EAAE,GAAG;AACnF", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/utils/statusConfig.js"], "sourcesContent": ["import { TriangleAlert, CheckCircle, Truck, Package, Clock } from \"lucide-react\";\r\n\r\nexport default function getStatusConfig(status) {\r\n    const configs = {\r\n        new: {\r\n            icon: Clock,\r\n            label: \"Новый\",\r\n            className: \"bg-orange-50 text-orange-600 border-orange-200\"\r\n        },\r\n        confirmed: {\r\n            icon: CheckCircle,\r\n            label: \"Подтвержден\",\r\n            className: \"bg-blue-50 text-blue-600 border-blue-200\"\r\n        },\r\n        completed: {\r\n            icon: Package,\r\n            label: \"Готов\",\r\n            className: \"bg-green-50 text-green-600 border-green-200\"\r\n        },\r\n        delivery: {\r\n            icon: Truck,\r\n            label: \"В пути\",\r\n            className: \"bg-purple-50 text-purple-600 border-purple-200\"\r\n        },\r\n        cancelled: {\r\n            icon: Triangle<PERSON>ler<PERSON>,\r\n            label: \"Отменен\",\r\n            className: \"bg-red-50 text-red-600 border-red-200\"\r\n        }\r\n    };\r\n    return configs[status] || configs.new;\r\n}"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AAAA;;AAEe,SAAS,gBAAgB,MAAM;IAC1C,MAAM,UAAU;QACZ,KAAK;YACD,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,WAAW;QACf;QACA,WAAW;YACP,MAAM,2NAAA,CAAA,cAAW;YACjB,OAAO;YACP,WAAW;QACf;QACA,WAAW;YACP,MAAM,wMAAA,CAAA,UAAO;YACb,OAAO;YACP,WAAW;QACf;QACA,UAAU;YACN,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,WAAW;QACf;QACA,WAAW;YACP,MAAM,wNAAA,CAAA,gBAAa;YACnB,OAAO;YACP,WAAW;QACf;IACJ;IACA,OAAO,OAAO,CAAC,OAAO,IAAI,QAAQ,GAAG;AACzC", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/lib/utils.js"], "sourcesContent": ["import { clsx } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAM;IAC1B,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/ui/NextStatusButton.jsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from \"react\";\r\nimport getNextStatus from \"@/utils/getNextStatus\";\r\nimport getStatusConfig from \"@/utils/statusConfig\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nexport default function NextStatusButton({ currentStatus }) {\r\n    const [nextStatus, setNextStatus] = useState(getNextStatus(currentStatus));\r\n    const statusConfig = getStatusConfig(nextStatus);\r\n\r\n    return (\r\n        <button className={cn(\"sticky bottom-0 w-full h-12 text-md font-semibold px-4 py-2\", statusConfig.className)}>\r\n            {statusConfig.label}\r\n        </button>\r\n    )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS,iBAAiB,EAAE,aAAa,EAAE;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,6HAAA,CAAA,UAAa,AAAD,EAAE;IAC3D,MAAM,eAAe,CAAA,GAAA,4HAAA,CAAA,UAAe,AAAD,EAAE;IAErC,qBACI,8OAAC;QAAO,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+DAA+D,aAAa,SAAS;kBACtG,aAAa,KAAK;;;;;;AAG/B", "debugId": null}}]}