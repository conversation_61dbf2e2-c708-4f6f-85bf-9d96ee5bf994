{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/constants/orderStatus.js"], "sourcesContent": ["/**\n * Enum для статусов заказов\n * Содержит все возможные статусы заказов и их строковые значения\n */\nexport const ORDER_STATUS = {\n  NEW: 'new',\n  CONFIRMED: 'confirmed', \n  COMPLETED: 'completed',\n  DELIVERY: 'delivery',\n  CANCELLED: 'cancelled'\n};\n\n/**\n * Массив статусов в порядке их следования в жизненном цикле заказа\n */\nexport const STATUS_ORDER = [\n  ORDER_STATUS.NEW,\n  ORDER_STATUS.CONFIRMED,\n  ORDER_STATUS.COMPLETED,\n  ORDER_STATUS.DELIVERY,\n  ORDER_STATUS.CANCELLED\n];\n\n/**\n * Проверяет, является ли переданное значение валидным статусом заказа\n * @param {string} status - Статус для проверки\n * @returns {boolean} - true если статус валидный\n */\nexport const isValidStatus = (status) => {\n  return Object.values(ORDER_STATUS).includes(status);\n};\n\n/**\n * Получает все возможные статусы заказов\n * @returns {string[]} - Массив всех статусов\n */\nexport const getAllStatuses = () => {\n  return Object.values(ORDER_STATUS);\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AACM,MAAM,eAAe;IAC1B,KAAK;IACL,WAAW;IACX,WAAW;IACX,UAAU;IACV,WAAW;AACb;AAKO,MAAM,eAAe;IAC1B,aAAa,GAAG;IAChB,aAAa,SAAS;IACtB,aAAa,SAAS;IACtB,aAAa,QAAQ;IACrB,aAAa,SAAS;CACvB;AAOM,MAAM,gBAAgB,CAAC;IAC5B,OAAO,OAAO,MAAM,CAAC,cAAc,QAAQ,CAAC;AAC9C;AAMO,MAAM,iBAAiB;IAC5B,OAAO,OAAO,MAAM,CAAC;AACvB", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/utils/getNextStatus.js"], "sourcesContent": ["import { STATUS_ORDER } from '@/constants/orderStatus';\r\n\r\nexport default function getNextStatus(status) {\r\n    const currentIndex = STATUS_ORDER.indexOf(status);\r\n    return currentIndex < STATUS_ORDER.length - 1 ? STATUS_ORDER[currentIndex + 1] : status;\r\n}"], "names": [], "mappings": ";;;AAAA;;AAEe,SAAS,cAAc,MAAM;IACxC,MAAM,eAAe,kIAAA,CAAA,eAAY,CAAC,OAAO,CAAC;IAC1C,OAAO,eAAe,kIAAA,CAAA,eAAY,CAAC,MAAM,GAAG,IAAI,kIAAA,CAAA,eAAY,CAAC,eAAe,EAAE,GAAG;AACrF", "debugId": null}}, {"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/utils/statusConfig.js"], "sourcesContent": ["import { <PERSON><PERSON><PERSON>t, CheckCircle, Truck, Package, Clock } from \"lucide-react\";\r\nimport { ORDER_STATUS } from '@/constants/orderStatus';\r\n\r\nexport default function getStatusConfig(status) {\r\n    const configs = {\r\n        [ORDER_STATUS.NEW]: {\r\n            icon: Clock,\r\n            label: \"Новый\",\r\n            className: \"bg-orange-50 text-orange-600 border-orange-200\"\r\n        },\r\n        [ORDER_STATUS.CONFIRMED]: {\r\n            icon: CheckCircle,\r\n            label: \"Подтвержден\",\r\n            className: \"bg-blue-50 text-blue-600 border-blue-200\"\r\n        },\r\n        [ORDER_STATUS.COMPLETED]: {\r\n            icon: Package,\r\n            label: \"Готов\",\r\n            className: \"bg-green-50 text-green-600 border-green-200\"\r\n        },\r\n        [ORDER_STATUS.DELIVERY]: {\r\n            icon: Truck,\r\n            label: \"В пути\",\r\n            className: \"bg-purple-50 text-purple-600 border-purple-200\"\r\n        },\r\n        [ORDER_STATUS.CANCELLED]: {\r\n            icon: <PERSON><PERSON><PERSON><PERSON>,\r\n            label: \"Отменен\",\r\n            className: \"bg-red-50 text-red-600 border-red-200\"\r\n        }\r\n    };\r\n    return configs[status] || configs[ORDER_STATUS.NEW];\r\n}"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAEe,SAAS,gBAAgB,MAAM;IAC1C,MAAM,UAAU;QACZ,CAAC,kIAAA,CAAA,eAAY,CAAC,GAAG,CAAC,EAAE;YAChB,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,WAAW;QACf;QACA,CAAC,kIAAA,CAAA,eAAY,CAAC,SAAS,CAAC,EAAE;YACtB,MAAM,8NAAA,CAAA,cAAW;YACjB,OAAO;YACP,WAAW;QACf;QACA,CAAC,kIAAA,CAAA,eAAY,CAAC,SAAS,CAAC,EAAE;YACtB,MAAM,2MAAA,CAAA,UAAO;YACb,OAAO;YACP,WAAW;QACf;QACA,CAAC,kIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,EAAE;YACrB,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,WAAW;QACf;QACA,CAAC,kIAAA,CAAA,eAAY,CAAC,SAAS,CAAC,EAAE;YACtB,MAAM,2NAAA,CAAA,gBAAa;YACnB,OAAO;YACP,WAAW;QACf;IACJ;IACA,OAAO,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,kIAAA,CAAA,eAAY,CAAC,GAAG,CAAC;AACvD", "debugId": null}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/lib/utils.js"], "sourcesContent": ["import { clsx } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAS;;IAC1B,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/ui/NextStatusButton.jsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from \"react\";\r\nimport getNextStatus from \"@/utils/getNextStatus\";\r\nimport getStatusConfig from \"@/utils/statusConfig\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nexport default function NextStatusButton({ currentStatus }) {\r\n    const [nextStatus, setNextStatus] = useState(getNextStatus(currentStatus));\r\n    const statusConfig = getStatusConfig(nextStatus);\r\n\r\n    return (\r\n        <button className={cn(\"sticky bottom-0 w-full h-12 text-md font-semibold px-4 py-2 uppercase\", statusConfig.className)}>\r\n            {statusConfig.label}\r\n        </button>\r\n    )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS,iBAAiB,KAAiB;QAAjB,EAAE,aAAa,EAAE,GAAjB;;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,gIAAA,CAAA,UAAa,AAAD,EAAE;IAC3D,MAAM,eAAe,CAAA,GAAA,+HAAA,CAAA,UAAe,AAAD,EAAE;IAErC,qBACI,6LAAC;QAAO,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yEAAyE,aAAa,SAAS;kBAChH,aAAa,KAAK;;;;;;AAG/B;GATwB;KAAA", "debugId": null}}]}