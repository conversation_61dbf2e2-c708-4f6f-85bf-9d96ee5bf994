import { cn } from "@/lib/utils";
import Container from "./Container";

export default function Section({ 
  children, 
  className,
  containerClassName,
  containerSize = "default",
  spacing = "default",
  background = "transparent",
  as: Component = "section",
  ...props 
}) {
  const spacingVariants = {
    none: "",
    sm: "py-8 md:py-12",
    default: "py-12 md:py-16 lg:py-20",
    lg: "py-16 md:py-20 lg:py-24",
    xl: "py-20 md:py-24 lg:py-32"
  };

  const backgroundVariants = {
    transparent: "",
    white: "bg-white",
    gray: "bg-gray-50",
    primary: "bg-primary/5",
    muted: "bg-muted"
  };

  return (
    <Component
      className={cn(
        // Базовые стили секции
        "relative w-full",
        
        // Отступы
        spacingVariants[spacing],
        
        // Фон
        backgroundVariants[background],
        
        className
      )}
      {...props}
    >
      <Container 
        size={containerSize}
        className={containerClassName}
      >
        {children}
      </Container>
    </Component>
  );
}
