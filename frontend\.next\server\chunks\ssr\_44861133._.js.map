{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/utils/getNextStatus.js"], "sourcesContent": ["export default function getNextStatus(status) {\r\n    const statusOrder = [\"new\", \"confirmed\", \"completed\", \"delivery\", \"cancelled\"];\r\n    const currentIndex = statusOrder.indexOf(status);\r\n    return currentIndex < statusOrder.length - 1 ? statusOrder[currentIndex + 1] : status;\r\n}"], "names": [], "mappings": ";;;AAAe,SAAS,cAAc,MAAM;IACxC,MAAM,cAAc;QAAC;QAAO;QAAa;QAAa;QAAY;KAAY;IAC9E,MAAM,eAAe,YAAY,OAAO,CAAC;IACzC,OAAO,eAAe,YAAY,MAAM,GAAG,IAAI,WAAW,CAAC,eAAe,EAAE,GAAG;AACnF", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/ui/NextStatusButton.jsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from \"react\";\r\nimport getNextStatus from \"@/utils/getNextStatus\";\r\n\r\nexport default function NextStatusButton({ currentStatus }) {\r\n    const [nextStatus, setNextStatus] = useState(getNextStatus(currentStatus));\r\n\r\n    return (\r\n        <button className=\"sticky bottom-0 w-full h-12 text-sm font-medium px-4 py-2 bg-blue-500 text-white hover:bg-blue-600\">\r\n            {nextStatus}\r\n        </button>\r\n    )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS,iBAAiB,EAAE,aAAa,EAAE;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,6HAAA,CAAA,UAAa,AAAD,EAAE;IAE3D,qBACI,8OAAC;QAAO,WAAU;kBACb;;;;;;AAGb", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}]}