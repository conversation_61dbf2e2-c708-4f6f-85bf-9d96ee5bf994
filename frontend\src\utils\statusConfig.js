import { <PERSON><PERSON><PERSON>t, CheckCircle, Truck, Package, Clock } from "lucide-react";
import { ORDER_STATUS } from '@/constants/orderStatus';

export default function getStatusConfig(status) {
    const configs = {
        [ORDER_STATUS.NEW]: {
            icon: Clock,
            label: "Новый",
            className: "bg-orange-50 text-orange-600 border-orange-200"
        },
        [ORDER_STATUS.CONFIRMED]: {
            icon: CheckCircle,
            label: "Подтвержден",
            className: "bg-blue-50 text-blue-600 border-blue-200"
        },
        [ORDER_STATUS.COMPLETED]: {
            icon: Package,
            label: "Готов",
            className: "bg-green-50 text-green-600 border-green-200"
        },
        [ORDER_STATUS.DELIVERY]: {
            icon: Truck,
            label: "В пути",
            className: "bg-purple-50 text-purple-600 border-purple-200"
        },
        [ORDER_STATUS.CANCELLED]: {
            icon: <PERSON><PERSON><PERSON><PERSON>,
            label: "Отменен",
            className: "bg-red-50 text-red-600 border-red-200"
        }
    };
    return configs[status] || configs[ORDER_STATUS.NEW];
}