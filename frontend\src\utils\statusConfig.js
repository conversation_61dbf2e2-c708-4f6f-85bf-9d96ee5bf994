import { TriangleAlert, CheckCircle, Truck, Package, Clock } from "lucide-react";

export default function getStatusConfig(status) {
    const configs = {
        new: {
            icon: Clock,
            label: "Новый",
            className: "bg-orange-50 text-orange-600 border-orange-200"
        },
        confirmed: {
            icon: CheckCircle,
            label: "Подтвержден",
            className: "bg-blue-50 text-blue-600 border-blue-200"
        },
        completed: {
            icon: Package,
            label: "Готов",
            className: "bg-green-50 text-green-600 border-green-200"
        },
        delivery: {
            icon: Truck,
            label: "В пути",
            className: "bg-purple-50 text-purple-600 border-purple-200"
        },
        cancelled: {
            icon: Triangle<PERSON>ler<PERSON>,
            label: "Отменен",
            className: "bg-red-50 text-red-600 border-red-200"
        }
    };
    return configs[status] || configs.new;
}