'use client'

import { useState } from "react";
import getNextStatus from "@/utils/getNextStatus";
import getStatusConfig from "@/utils/statusConfig";
import { cn } from "@/lib/utils";

export default function NextStatusButton({ currentStatus }) {
    const [nextStatus, setNextStatus] = useState(getNextStatus(currentStatus));
    const statusConfig = getStatusConfig(nextStatus);

    return (
        <button className={cn("sticky bottom-0 w-full h-12 text-md font-semibold px-4 py-2", statusConfig.className)}>
            {statusConfig.label}
        </button>
    )
}