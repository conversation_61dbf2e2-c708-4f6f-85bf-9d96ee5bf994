'use client'

import { useEffect, useState } from "react";
import getNextStatus from "@/utils/getNextStatus";
import getStatusConfig from "@/utils/statusConfig";
import { cn } from "@/lib/utils";

export default function NextStatusButton({ currentStatus }) {
    const [nextStatus, setNextStatus] = useState(getNextStatus(currentStatus))
    const statusConfig = getStatusConfig(nextStatus)
    const StatusIcon = statusConfig.icon

    useEffect(() => {
        setNextStatus(getNextStatus(currentStatus))
    }, [currentStatus])

    return (
        <button className={cn("sticky bottom-0 w-full h-16 text-md font-black px-4 py-2 uppercase cursor-pointer", statusConfig.className)}>
            <div className="inline-flex items-center gap-2 px-2 py-1">
                <StatusIcon className="w-4 h-4" />
                {statusConfig.label}
            </div>
        </button>
    )
}