/**
 * Enum для статусов заказов
 * Содержит все возможные статусы заказов и их строковые значения
 */
export const ORDER_STATUS = {
  NEW: 'new',
  CONFIRMED: 'confirmed', 
  COMPLETED: 'completed',
  DELIVERY: 'delivery',
  CANCELLED: 'cancelled'
};

/**
 * Массив статусов в порядке их следования в жизненном цикле заказа
 */
export const STATUS_ORDER = [
  ORDER_STATUS.NEW,
  ORDER_STATUS.CONFIRMED,
  ORDER_STATUS.COMPLETED,
  ORDER_STATUS.DELIVERY,
  ORDER_STATUS.CANCELLED
];

/**
 * Проверяет, является ли переданное значение валидным статусом заказа
 * @param {string} status - Статус для проверки
 * @returns {boolean} - true если статус валидный
 */
export const isValidStatus = (status) => {
  return Object.values(ORDER_STATUS).includes(status);
};

/**
 * Получает все возможные статусы заказов
 * @returns {string[]} - Массив всех статусов
 */
export const getAllStatuses = () => {
  return Object.values(ORDER_STATUS);
};
