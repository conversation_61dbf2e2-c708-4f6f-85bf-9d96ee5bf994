import { cn } from "@/lib/utils";

export default function Container({
  children,
  className,
  size = "default",
  as: Component = "div",
  ...props
}) {
  const sizeVariants = {
    sm: "max-w-4xl",
    default: "max-w-6xl",
    lg: "max-w-7xl",
    full: "max-w-none"
  };

  return (
    <Component
      className={cn(
        "w-full mx-auto transition-all duration-300 ease-in-out",

        sizeVariants[size],

        "px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16",

        "relative",

        className
      )}
      {...props}
    >
      {children}
    </Component>
  );
}