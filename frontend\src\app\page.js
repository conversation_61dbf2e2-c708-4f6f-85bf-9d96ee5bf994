import { Section } from "@/components/layout";
import OrderReviewCard from "@/components/shared/OrderReviewCard";
import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { Card, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Clock, CheckCircle, Truck, Package } from "lucide-react";
import { ORDER_STATUS } from '@/constants/orderStatus';

export default function Home() {
  // Преобразованные данные заказа для компонента OrderItem
  const orderData = {
    id: "**********",
    user: {
      first_name: "Даня",
      phone: "+7 (950) 079-32-65"
    },
    delivery_type: "Доставка",
    comment: "Тестовый заказ, не пробовать",
    address: "", // Пустой для самовывоза
    paymentsystem: "banktransfer",
    status: ORDER_STATUS.NEW,
    amount: "3150",
    meals: [
      {
        id: "ehnOzTB06KH0dpL2HiZP",
        name: "Окрошка на Квасе",
        quantity: 1400,
        amount: 1400,
        img: "https://static.tildacdn.com/stor3233-3161-4831-b432-************/********.jpg",
        pack_m: "350",
        price: "1",
        unit: "г",
        portion: "350"
      },
      {
        id: "meal2",
        name: "Борщ украинский",
        quantity: 700,
        amount: 700,
        img: "https://static.tildacdn.com/stor3233-3161-4831-b432-************/********.jpg",
        pack_m: "350",
        price: "1",
        unit: "г",
        portion: "350"
      },
      {
        id: "meal3",
        name: "Плов узбекский",
        quantity: 1050,
        amount: 1050,
        img: "https://static.tildacdn.com/stor3233-3161-4831-b432-************/********.jpg",
        pack_m: "350",
        price: "1",
        unit: "г",
        portion: "350"
      }
    ]
  };

  const statusSections = [
    {
      id: "item-1",
      title: "Новые заказы",
      count: 3,
      icon: Clock,
      color: "text-orange-600",
      bgColor: "bg-orange-50",
      borderColor: "border-orange-200"
    },
    {
      id: "item-2",
      title: "Подтверждены",
      count: 10,
      icon: CheckCircle,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
      borderColor: "border-blue-200"
    },
    {
      id: "item-3",
      title: "Готовы",
      count: 2,
      icon: Package,
      color: "text-green-600",
      bgColor: "bg-green-50",
      borderColor: "border-green-200"
    },
    {
      id: "item-4",
      title: "В пути",
      count: 80,
      icon: Truck,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
      borderColor: "border-purple-200"
    }
  ];

  return (
    <Section spacing="sm">
      <Accordion
        type="multiple"
        className="space-y-3 bg-gray-50 rounded-xl p-4"
      >
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Заказы</h1>
        {statusSections.map((section) => {
          const IconComponent = section.icon;
          return (
            <AccordionItem
              key={section.id}
              value={section.id}
              className="bg-white rounded-xl border border-gray-100 overflow-hidden"
            >
              <AccordionTrigger className="text-lg font-semibold text-gray-900 hover:no-underline px-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <IconComponent className={`w-5 h-5 ${section.color}`} />
                    <span>{section.title}</span>
                  </div>
                  <Badge
                    variant="secondary"
                    className={`font-mono ${section.bgColor} ${section.color} border-0 ml-2`}
                  >
                    {section.count}
                  </Badge>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4">
                <OrderReviewCard order={orderData} />
                <OrderReviewCard order={orderData} />
                <OrderReviewCard order={orderData} />
              </AccordionContent>
            </AccordionItem>
          );
        })}
      </Accordion>
    </Section>
  );
}
